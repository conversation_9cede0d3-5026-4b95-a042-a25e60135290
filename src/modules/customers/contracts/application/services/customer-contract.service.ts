import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { CreateContractsDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { Contract } from '@/modules/documents/domain/entities/contract.entity';
import { CreateContractUseCase } from '@/modules/documents/application/use-cases/create-contract.use-case';
import { ListContractUseCase } from '@/modules/documents/application/use-cases/list-contract.use-case';
import { DownloadContractUseCase } from '@/modules/documents/application/use-cases/download-contract.use-case';
import { DeleteContractUseCase } from '@/modules/documents/application/use-cases/delete-contract.use-case';

import { EntityType } from '@/modules/documents/domain/enums/entity-type.enum';
import { ContractStatus } from '@prisma/client';
import { ContractSignPatchDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { IContractRepository } from '@/modules/documents/domain/repositories/contract.repository.interface';

@Injectable()
export class CustomerContractService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(CreateContractUseCase)
    private readonly createContractUseCase: CreateContractUseCase,
    @Inject(ListContractUseCase)
    private readonly listContractUseCase: ListContractUseCase,
    @Inject(DownloadContractUseCase)
    private readonly downloadContractUseCase: DownloadContractUseCase,
    @Inject(DeleteContractUseCase)
    private readonly deleteContractUseCase: DeleteContractUseCase,

    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,
  ) { }

  async createCustomerContract(
    customerUuid: string,
    files: Express.Multer.File[],
    userId: string,
    createContractsDto: CreateContractsDto,
  ): Promise<Contract[]> {
    const customerExists = await this.prisma.customer.findUnique({
      where: { uuid: customerUuid },
    });
    if (!customerExists) {
      throw new NotFoundException(
        `Cliente com UUID ${customerUuid} não encontrado.`,
      );
    }

    const populatedContracts = createContractsDto.contracts.map((contract) => ({
      ...contract,
      entityActualUuid: customerUuid,
    }));

    const populatedDto: CreateContractsDto = {
      ...createContractsDto,
      contracts: populatedContracts,
    };

    const contracts = await this.createContractUseCase.execute(
      customerUuid,
      populatedDto,
      files,
      userId,
    );

    return contracts;
  }

  async listCustomerContracts(customerUuid: string): Promise<Contract[]> {
    const customerExists = await this.prisma.customer.findUnique({
      where: { uuid: customerUuid },
    });
    if (!customerExists) {
      throw new NotFoundException(
        `Cliente com UUID ${customerUuid} não encontrado.`,
      );
    }

    const { items } = await this.listContractUseCase.execute(
      {
        entityType: EntityType.CUSTOMER,
        entityUuid: customerUuid,
      },
      100,
      0,
    );

    // Gerar URLs de download para todos os contratos
    const contractsWithUrls = await Promise.all(
      items.map(async (contract) => {
        if (contract.versions && contract.versions.length > 0) {
          try {
            // Gerar URL para a versão atual
            const currentVersion = contract.versions.find(v => v.versionId === contract.currentVersion);
            if (currentVersion) {
              const { url, fileName } = await this.downloadContractUseCase.execute(contract.uuid);
              return {
                ...contract,
                downloadUrl: url,
                fileName: fileName,
              };
            }
          } catch (error) {
            console.error(`Erro ao gerar URL de download para contrato ${contract.uuid}:`, error);
          }
        }
        return contract;
      })
    );

    return contractsWithUrls;
  }

  async getCustomerContract(customerUuid: string, contractUuid: string): Promise<Contract | null> {
    const customerExists = await this.prisma.customer.findUnique({
      where: { uuid: customerUuid },
    });
    if (!customerExists) {
      throw new NotFoundException(
        `Cliente com UUID ${customerUuid} não encontrado.`,
      );
    }

    const contract = await this.contractRepository.findByUuid(contractUuid);

    if (!contract || contract.entityUuid !== customerUuid) {
      return null;
    }

    return contract;
  }

  async updateCustomerContract(
    customerUuid: string,
    contractUuid: string,
    data: ContractSignPatchDto,
  ): Promise<Contract> {
    const customerExists = await this.prisma.customer.findUnique({
      where: { uuid: customerUuid },
    });
    if (!customerExists) {
      throw new NotFoundException(
        `Cliente com UUID ${customerUuid} não encontrado.`,
      );
    }

    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract || contract.entityUuid !== customerUuid) {
      throw new NotFoundException(
        `Contrato com UUID ${contractUuid} não encontrado para este cliente.`,
      );
    }

    // Cria nova versão do contrato com os dados já existentes, alterando apenas o signed
    await this.contractRepository.createVersion(contractUuid, {
      uploadedBy: contract.createdBy,
      filePath: contract.versions?.find(v => v.versionId === contract.currentVersion)?.filePath || '',
      signed: data.isSigned,
      startDate: (() => {
        const start = contract.versions?.find(v => v.versionId === contract.currentVersion)?.startDate;
        return start === null ? undefined : start;
      })(),
      expirationDate: (() => {
        const exp = contract.versions?.find(v => v.versionId === contract.currentVersion)?.expirationDate;
        return exp === null ? undefined : exp;
      })(),
      observations: (() => {
        const obs = contract.versions?.find(v => v.versionId === contract.currentVersion)?.observations;
        return obs === null ? undefined : obs;
      })(),
    });

    return this.contractRepository.update(contractUuid, {
      status: data.isSigned ? ContractStatus.APPROVED : ContractStatus.REJECTED,
    });
  }

  async deleteCustomerContract(customerUuid: string, contractUuid: string): Promise<void> {
    const customerExists = await this.prisma.customer.findUnique({
      where: { uuid: customerUuid },
    });
    if (!customerExists) {
      throw new NotFoundException(
        `Cliente com UUID ${customerUuid} não encontrado.`,
      );
    }

    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract || contract.entityUuid !== customerUuid) {
      throw new NotFoundException(
        `Contrato com UUID ${contractUuid} não encontrado para este cliente.`,
      );
    }

    await this.deleteContractUseCase.execute(contractUuid);
  }
} 