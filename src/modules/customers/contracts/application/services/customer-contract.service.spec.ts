import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { CustomerContractService } from './customer-contract.service';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { CreateContractUseCase } from '@/modules/documents/application/use-cases/create-contract.use-case';
import { ListContractUseCase } from '@/modules/documents/application/use-cases/list-contract.use-case';
import { DownloadContractUseCase } from '@/modules/documents/application/use-cases/download-contract.use-case';
import { DeleteContractUseCase } from '@/modules/documents/application/use-cases/delete-contract.use-case';
import { IContractRepository } from '@/modules/documents/domain/repositories/contract.repository.interface';
import { CreateContractsDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { Contract } from '@/modules/documents/domain/entities/contract.entity';
import { EntityType } from '@/modules/documents/domain/enums/entity-type.enum';
import { ContractStatus, ContractType } from '@prisma/client';

describe('CustomerContractService', () => {
  let service: CustomerContractService;
  let prismaService: jest.Mocked<PrismaService>;
  let createContractUseCase: jest.Mocked<CreateContractUseCase>;
  let listContractUseCase: jest.Mocked<ListContractUseCase>;
  let downloadContractUseCase: jest.Mocked<DownloadContractUseCase>;
  let deleteContractUseCase: jest.Mocked<DeleteContractUseCase>;
  let contractRepository: jest.Mocked<IContractRepository>;

  const mockCustomer = {
    uuid: 'customer-uuid',
    name: 'Test Customer',
    email: '<EMAIL>',
    document: '12345678901',
  };

  const mockContract: Contract = {
    uuid: 'contract-uuid',
    entityUuid: 'customer-uuid',
    entityType: EntityType.CUSTOMER,
    status: ContractStatus.PENDING,
    contractType: ContractType.CERT_PLATFORM,
    createdBy: 'user-id',
    uptadedBy: 'user-id',
    currentVersion: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    versions: [
      {
        id: 'version-id',
        versionId: 1,
        filePath: '/path/to/file.pdf',
        uploadedAt: new Date(),
        uploadedBy: 'user-id',
        signed: false,
        validatedBy: null,
        validatedAt: null,
        startDate: null,
        expirationDate: null,
        observations: null,
        contractId: 'contract-uuid',
        createdAt: new Date(),
      },
    ],
  };

  beforeEach(async () => {
    const mockPrismaService = {
      customer: {
        findUnique: jest.fn(),
      },
    } as any; // Corrigido para permitir mockResolvedValue

    const mockCreateContractUseCase = {
      execute: jest.fn(),
    };

    const mockListContractUseCase = {
      execute: jest.fn(),
    };

    const mockDownloadContractUseCase = {
      execute: jest.fn(),
    };

    const mockDeleteContractUseCase = {
      execute: jest.fn(),
    };

    const mockContractRepository = {
      findByUuid: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      createVersion: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerContractService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: CreateContractUseCase,
          useValue: mockCreateContractUseCase,
        },
        {
          provide: ListContractUseCase,
          useValue: mockListContractUseCase,
        },
        {
          provide: DownloadContractUseCase,
          useValue: mockDownloadContractUseCase,
        },
        {
          provide: DeleteContractUseCase,
          useValue: mockDeleteContractUseCase,
        },
        {
          provide: 'IContractRepository',
          useValue: mockContractRepository,
        },
      ],
    }).compile();

    service = module.get<CustomerContractService>(CustomerContractService);
    prismaService = module.get(PrismaService);
    createContractUseCase = module.get(CreateContractUseCase);
    listContractUseCase = module.get(ListContractUseCase);
    downloadContractUseCase = module.get(DownloadContractUseCase);
    deleteContractUseCase = module.get(DeleteContractUseCase);
    contractRepository = module.get('IContractRepository');
  });

  describe('createCustomerContract', () => {
    const mockFiles = [
      {
        fieldname: 'files',
        originalname: 'test.pdf',
        encoding: '7bit',
        mimetype: 'application/pdf',
        buffer: Buffer.from('test content'),
        size: 1024,
      } as Express.Multer.File,
    ];

    const mockCreateContractsDto: CreateContractsDto = {
      contracts: [
        {
          contractIdentifier: 'test-contract',
          entityType: EntityType.CUSTOMER,
          entityActualUuid: 'customer-uuid',
          contractType: 'CERT_PLATFORM',
          signed: false,
          expirationDate: '2025-12-31',
        },
      ],
    };

    it('should create customer contract successfully', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      createContractUseCase.execute.mockResolvedValue([mockContract]);

      const result = await service.createCustomerContract(
        'customer-uuid',
        mockFiles,
        'user-id',
        mockCreateContractsDto,
      );

      expect(prismaService.customer.findUnique).toHaveBeenCalledWith({
        where: { uuid: 'customer-uuid' },
      });
      expect(createContractUseCase.execute).toHaveBeenCalledWith(
        'customer-uuid',
        {
          contracts: [
            {
              ...mockCreateContractsDto.contracts[0],
              entityActualUuid: 'customer-uuid',
            },
          ],
        },
        mockFiles,
        'user-id',
      );
      expect(result).toEqual([mockContract]);
    });

    it('should throw NotFoundException when customer does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(
        service.createCustomerContract(
          'non-existent-uuid',
          mockFiles,
          'user-id',
          mockCreateContractsDto,
        ),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('listCustomerContracts', () => {
    it('should list customer contracts successfully', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      listContractUseCase.execute.mockResolvedValue({
        items: [mockContract],
        total: 1,
      });
      downloadContractUseCase.execute.mockResolvedValue({
        url: 'https://example.com/download',
        fileName: 'test.pdf',
      });

      const result = await service.listCustomerContracts('customer-uuid');

      expect(prismaService.customer.findUnique).toHaveBeenCalledWith({
        where: { uuid: 'customer-uuid' },
      });
      expect(listContractUseCase.execute).toHaveBeenCalledWith(
        {
          entityType: EntityType.CUSTOMER,
          entityUuid: 'customer-uuid',
        },
        100,
        0,
      );
      expect(result).toHaveLength(1);
    });

    it('should throw NotFoundException when customer does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(
        service.listCustomerContracts('non-existent-uuid'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getCustomerContract', () => {
    it('should get specific customer contract successfully', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      contractRepository.findByUuid.mockResolvedValue(mockContract);

      const result = await service.getCustomerContract('customer-uuid', 'contract-uuid');

      expect(prismaService.customer.findUnique).toHaveBeenCalledWith({
        where: { uuid: 'customer-uuid' },
      });
      expect(contractRepository.findByUuid).toHaveBeenCalledWith('contract-uuid');
      expect(result).toEqual(mockContract);
    });

    it('should return null when contract does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      contractRepository.findByUuid.mockResolvedValue(null);

      const result = await service.getCustomerContract('customer-uuid', 'non-existent-uuid');

      expect(result).toBeNull();
    });

    it('should throw NotFoundException when customer does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(
        service.getCustomerContract('non-existent-uuid', 'contract-uuid'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateCustomerContract', () => {
    const mockUpdateData = {
      isSigned: true,
    };

    it('should update customer contract successfully', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      contractRepository.findByUuid.mockResolvedValue(mockContract);
      contractRepository.update.mockResolvedValue({
        ...mockContract,
        versions: [
          {
            ...mockContract.versions![0],
            signed: true,
          },
        ],
      });

      const result = await service.updateCustomerContract(
        'customer-uuid',
        'contract-uuid',
        mockUpdateData,
      );

      expect(prismaService.customer.findUnique).toHaveBeenCalledWith({
        where: { uuid: 'customer-uuid' },
      });
      expect(contractRepository.findByUuid).toHaveBeenCalledWith('contract-uuid');
      expect(contractRepository.update).toHaveBeenCalledWith('contract-uuid', { status: ContractStatus.APPROVED });
      expect(result).toBeDefined();
    });

    it('should throw NotFoundException when customer does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(
        service.updateCustomerContract('non-existent-uuid', 'contract-uuid', mockUpdateData),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when contract does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      contractRepository.findByUuid.mockResolvedValue(null);

      await expect(
        service.updateCustomerContract('customer-uuid', 'non-existent-uuid', mockUpdateData),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteCustomerContract', () => {
    it('should delete customer contract successfully', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      contractRepository.findByUuid.mockResolvedValue(mockContract);
      deleteContractUseCase.execute.mockResolvedValue(undefined);

      await service.deleteCustomerContract('customer-uuid', 'contract-uuid');

      expect(prismaService.customer.findUnique).toHaveBeenCalledWith({
        where: { uuid: 'customer-uuid' },
      });
      expect(contractRepository.findByUuid).toHaveBeenCalledWith('contract-uuid');
      expect(deleteContractUseCase.execute).toHaveBeenCalledWith('contract-uuid');
    });

    it('should throw NotFoundException when customer does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(
        service.deleteCustomerContract('non-existent-uuid', 'contract-uuid'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when contract does not exist', async () => {
      (prismaService.customer.findUnique as jest.Mock).mockResolvedValue(mockCustomer as any);
      contractRepository.findByUuid.mockResolvedValue(null);

      await expect(
        service.deleteCustomerContract('customer-uuid', 'non-existent-uuid'),
      ).rejects.toThrow(NotFoundException);
    });
  });
}); 