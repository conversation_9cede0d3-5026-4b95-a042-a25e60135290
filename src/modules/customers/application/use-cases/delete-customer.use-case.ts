import { Injectable, Inject, NotFoundException, Logger } from '@nestjs/common';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';

@Injectable()
export class DeleteCustomerUseCase {
  private readonly logger = new Logger(DeleteCustomerUseCase.name);

  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
    @Inject('UserRepository')
    private readonly userRepository: UserRepository,
    private readonly keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {}

  async execute(uuid: string): Promise<void> {
    const customer = await this.customerRepository.findByUuid(uuid);
    if (!customer) {
      throw new NotFoundException('Cliente não encontrado.');
    }

    if (customer.userId) {
      try {
        const user = await this.userRepository.findById(customer.userId);
        if (user && user.keycloakId) {
          await this.keycloakIdentityProvider.deleteUser(user.keycloakId);
        }
      } catch (error) {
        this.logger.warn(`Erro ao deletar usuário do customer ${uuid} no Keycloak:`, error);
      }
    }

    await this.customerRepository.delete(uuid);
  }
}
