import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID, IsInt, Max, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { DocumentStatus } from '../../domain/enums/document-status.enum';

export class FilterDocumentsDto {
  @ApiPropertyOptional({
    description: 'Tipo da entidade vinculada',
    enum: EntityType,
    example: EntityType.CUSTOMER,
  })
  @IsOptional()
  @IsEnum(EntityType)
  entityType?: EntityType;

  @ApiPropertyOptional({
    description: 'UUID da entidade vinculada',
    format: 'uuid',
    example: 'f3f8aefa-02b7-429c-8e02-8354cfe9184a',
  })
  @IsOptional()
  @IsUUID()
  entityUuid?: string;

  @ApiPropertyOptional({
    description: 'Status atual do documento',
    enum: DocumentStatus,
    example: DocumentStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiPropertyOptional({
    description: 'Filtrar documentos que expiram nos próximos X dias',
    example: 30,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  expiringWithin?: number;

  @ApiPropertyOptional({
    description: 'Número máximo de documentos por página',
    default: 20,
    example: 10,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Deslocamento de página (offset)',
    default: 0,
    example: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  offset?: number = 0;
}
