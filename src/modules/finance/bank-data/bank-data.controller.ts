/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
// @ts-nocheck
import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Param,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { BankDataService } from './bank-data.service';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
import { UpdateBankDataDto } from './dto/update-bank-data.dto';
import { GetBankDataQueryDto } from './dto/get-bank-data-query.dto';
import { CreateBankDataQueryDto } from './dto/create-bank-data-query.dto';

interface AuthenticatedRequest extends Request {
  user: {
    preferred_username: string;
    email: string;
  };
}

@ApiTags('Bank Data')
@Controller('finance/bank-data/')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BankDataController {
  constructor(private readonly bankDataService: BankDataService) {}

  private parseIntOrDefault(value: unknown, defaultValue: number): number {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? defaultValue : parsed;
    }
    return typeof value === 'number' ? value : defaultValue;
  }
  @Post('/')
  @Roles(
    Role.ADMIN,
    Role.FINANCE_ADMIN,
    Role.EMPLOYEE,
    Role.CUSTOMER,
    Role.CUSTOMER_VIEWER,
    Role.SUPPLIER,
    Role.SUPPLIER_VIEWER,
    Role.USER,
  )
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create bank data for user, customers, suppliers and employees',
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    type: String,
    description:
      'User UUID. Use this OR the combination of entityType + entityId (mutually exclusive)',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['CUSTOMER', 'COLLABORATE', 'SUPPLIER'],
    description:
      'Entity type. Required when userId is not provided. Must be used together with entityId',
    example: 'COLLABORATE',
  })
  @ApiQuery({
    name: 'entityId',
    required: false,
    type: String,
    description:
      'Entity UUID. Required when userId is not provided. Must be used together with entityType',
    example: 'bbbbbbbb-cccc-dddd-eeee-ffffffffffff',
  })
  @ApiCreatedResponse({
    description: 'Bank data created successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async create(
    @Body() createBankDataDto: CreateBankDataDto,
    @Request() req: AuthenticatedRequest,
    @Query() query: CreateBankDataQueryDto,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;

    const user = {
      userId: query.userId,
      entityType: query.entityType,
      entityId: query.entityId,
    };
    return this.bankDataService.create({
      requestUserEmail,
      user,
      createBankDataDto,
    });
  }

  @Put('/:uuid')
  @Roles(
    Role.ADMIN,
    Role.FINANCE_ADMIN,
    Role.EMPLOYEE,
    Role.CUSTOMER,
    Role.CUSTOMER_VIEWER,
    Role.SUPPLIER,
    Role.SUPPLIER_VIEWER,
    Role.USER,
  )
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update bank data for employees' })
  @ApiOkResponse({
    description: 'Bank data updated successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Bank data not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async update(
    @Param('uuid') uuid: string,
    @Body() updateBankDataDto: UpdateBankDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    const response = await this.bankDataService.update({
      requestUserEmail,
      bankDataParamId: uuid,
      updateBankDataDto,
    });
    return response.bankData;
  }

  @Get()
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List all bank data with pagination' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'employeeId',
    required: false,
    type: Number,
    description: 'Filter by employee ID',
    example: 123,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['ACTIVE', 'INACTIVE', 'PENDING'],
    description: 'Filter by bank data status',
    example: 'ACTIVE',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    enum: ['CUSTOMER', 'COLLABORATE', 'SUPPLIER'],
    description: 'Filter by entity type',
    example: 'COLLABORATE',
  })
  @ApiQuery({
    name: 'bankName',
    required: false,
    type: String,
    description: 'Filter by bank name (partial match, case insensitive)',
    example: 'Banco do Brasil',
  })
  @ApiQuery({
    name: 'bankCode',
    required: false,
    type: String,
    description: 'Filter by bank code (exact match)',
    example: '001',
  })
  @ApiQuery({
    name: 'accountType',
    required: false,
    enum: ['CHECKING', 'SAVINGS', 'SALARY'],
    description: 'Filter by account type',
    example: 'CHECKING',
  })
  @ApiQuery({
    name: 'agencyNumber',
    required: false,
    type: String,
    description: 'Filter by agency number (exact match)',
    example: '1234',
  })
  @ApiQuery({
    name: 'agencyDigit',
    required: false,
    type: String,
    description: 'Filter by agency digit (exact match)',
    example: '5',
  })
  @ApiQuery({
    name: 'accountNumber',
    required: false,
    type: String,
    description: 'Filter by account number (exact match)',
    example: '123456',
  })
  @ApiQuery({
    name: 'accountDigit',
    required: false,
    type: String,
    description: 'Filter by account digit (exact match)',
    example: '7',
  })
  @ApiQuery({
    name: 'accountHolderName',
    required: false,
    type: String,
    description:
      'Filter by account holder name (partial match, case insensitive)',
    example: 'João da Silva',
  })
  @ApiQuery({
    name: 'accountHolderDocument',
    required: false,
    type: String,
    description: 'Filter by account holder document (exact match)',
    example: '***********',
  })
  @ApiQuery({
    name: 'pixKey',
    required: false,
    type: String,
    description: 'Filter by PIX key (partial match, case insensitive)',
    example: '<EMAIL>',
  })
  @ApiQuery({
    name: 'pixKeyType',
    required: false,
    enum: ['EMAIL', 'PHONE', 'CPF', 'CNPJ', 'RANDOM'],
    description: 'Filter by PIX key type',
    example: 'EMAIL',
  })
  @ApiQuery({
    name: 'isDigitalBank',
    required: false,
    type: Boolean,
    description: 'Filter by digital bank status',
    example: false,
  })
  @ApiQuery({
    name: 'createdAt',
    required: false,
    type: String,
    description: 'Filter by creation date (ISO string format)',
    example: '2023-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'updatedAt',
    required: false,
    type: String,
    description: 'Filter by update date (ISO string format)',
    example: '2023-01-01T00:00:00.000Z',
  })
  @ApiOkResponse({
    description: 'Bank data list retrieved successfully',
    type: [BankDataResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findAll(@Query() query: GetBankDataQueryDto) {
    const limit = this.parseIntOrDefault(query.limit, 10);
    const page = this.parseIntOrDefault(query.page, 1);
    const offset = (page - 1) * limit;

    return this.bankDataService.findAll({
      limit,
      offset,
      query: {
        entityType: query.entityType,
        bankName: query.bankName,
        bankCode: query.bankCode,
        accountType: query.accountType,
        agencyNumber: query.agencyNumber,
        agencyDigit: query.agencyDigit,
        accountNumber: query.accountNumber,
        accountDigit: query.accountDigit,
        accountHolderName: query.accountHolderName,
        accountHolderDocument: query.accountHolderDocument,
        pixKey: query.pixKey,
        pixKeyType: query.pixKeyType,
        isDigitalBank: query.isDigitalBank,
        createdAt: query.createdAt ? new Date(query.createdAt) : undefined,
        updatedAt: query.updatedAt ? new Date(query.updatedAt) : undefined,
        status: query.status,
        employeeId: this.parseIntOrDefault(query.employeeId, 0),
      },
    });
  }

  @Get('/:id')
  @Roles(
    Role.ADMIN,
    Role.FINANCE_ADMIN,
    Role.EMPLOYEE,
    Role.CUSTOMER,
    Role.CUSTOMER_VIEWER,
    Role.SUPPLIER,
    Role.SUPPLIER_VIEWER,
    Role.USER,
  )
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get bank data by ID' })
  @ApiOkResponse({
    description: 'Bank data retrieved successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Bank data not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findById(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    return this.bankDataService.findById({ bankDataId: id, requestUserEmail });
  }
}
