import {
  Injectable,
  Inject,
  NotFoundException,
  Logger,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
import { UserDataResponseDto } from './dto/user-data-response.dto';

import { PrismaUserRepository } from '@/infrastructure/repositories/prisma-user.repository';
import { PrismaBankDataRepository } from '@/infrastructure/repositories/prisma-bank-data.repository';
import { PrismaEmployeeRepository } from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaSupplierRepository } from '@/infrastructure/repositories/prisma-supplier.repository';
import { PrismaCustomerRepository } from '@/infrastructure/repositories/prisma-customer.repository';

import { Role } from '@/core/domain/role.enum';
import { PrismaBankAccountUpsertDataRepository } from '@/infrastructure/repositories/prisma-bank-account-upsert-data';
import {
  BankAccountType,
  BankPixKeyType,
  EntityType,
  BankData as PrismaBankData,
  Prisma,
  BankDataStatus,
} from '@prisma/client';
import { User } from '@/core/domain/user.entity';
import { BANK_LIST } from './constants/bank-list.constant';
import { UpdateBankDataDto } from './dto/update-bank-data.dto';

type UserResponse = User & {
  employee?: {
    id: number;
    name: string;
    document: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  };
  supplier?: {
    id: number;
    name: string;
    document: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    type: string;
  };
  customer?: {
    id: number;
    name: string;
    document: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  };
};

type keysTypes = number | string | boolean | null;
type CreateBankDataQuery =
  | {
    userId: string;
    entityType?: undefined;
    entityId?: undefined;
  }
  | {
    userId?: undefined;
    entityType: EntityType;
    entityId: string;
  };
@Injectable()
export class BankDataService {
  private readonly logger = new Logger(BankDataService.name);
  constructor(
    @Inject('UserRepository')
    private readonly PrismaUserRepository: PrismaUserRepository,
    @Inject('BankDataRepository')
    private readonly PrismaBankDataRepository: PrismaBankDataRepository,
    @Inject('BankAccountUpsertDataRepository')
    private readonly PrismaBankAccountUpsertDataRepository: PrismaBankAccountUpsertDataRepository,
    @Inject('EmployeeRepository')
    private readonly PrismaEmployeeRepository: PrismaEmployeeRepository,
    @Inject('SupplierRepository')
    private readonly PrismaSupplierRepository: PrismaSupplierRepository,
    @Inject('CustomerRepository')
    private readonly PrismaCustomerRepository: PrismaCustomerRepository,
  ) { }

  async create({
    requestUserEmail,
    user,
    createBankDataDto,
  }: {
    requestUserEmail: string;
    user: CreateBankDataQuery;
    createBankDataDto: CreateBankDataDto;
  }): Promise<{ bankData: BankDataResponseDto; user: UserDataResponseDto }> {
    const requestUser =
      await this.PrismaUserRepository.findByEmail(requestUserEmail);
    if (!requestUser) {
      throw new NotFoundException('Request user not found');
    }

    let userParam: User | null = null;

    if (user.userId) {
      userParam = await this.PrismaUserRepository.findById(user.userId);
    }

    if (user.entityType && user.entityId && user.entityType === 'COLLABORATE') {
      const employee = await this.PrismaEmployeeRepository.findByUuid(
        user.entityId,
      );
      if (!employee || !employee.userId) {
        throw new NotFoundException('Employee not found');
      }

      if (!employee.userId) {
        throw new NotFoundException('User employee not found');
      }
      userParam = await this.PrismaUserRepository.findById(employee.userId);
    }

    if (user.entityType && user.entityId && user.entityType === EntityType.CUSTOMER) {
      const customer = await this.PrismaCustomerRepository.findByUuid(
        user.entityId,
      );

      if (!customer || !customer.userId) {
        throw new NotFoundException('Customer not found');
      }

      if (!customer.userId) {
        throw new NotFoundException('User customer not found');
      }
      userParam = await this.PrismaUserRepository.findById(customer.userId);
    }

    if (user.entityType && user.entityId && user.entityType === 'SUPPLIER') {
      const supplier = await this.PrismaSupplierRepository.findById(
        user.entityId,
      );

      if (!supplier || !supplier.userId) {
        throw new NotFoundException('Supplier not found');
      }

      if (!supplier.userId) {
        throw new NotFoundException('User supplier not found');
      }
      userParam = await this.PrismaUserRepository.findById(supplier.userId);
    }

    if (!userParam) {
      throw new NotFoundException('User not found');
    }

    const verifyBankDataExist =
      await this.PrismaBankDataRepository.findByEntityUuid(userParam.id);

    if (verifyBankDataExist) {
      throw new ConflictException('User already has bank data');
    }

    if (
      requestUser.id !== userParam.id &&
      requestUser.role !== Role.ADMIN &&
      requestUser.role !== Role.FINANCE_ADMIN
    ) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    if (
      requestUser.role !== Role.ADMIN &&
      requestUser.role !== Role.FINANCE_ADMIN
    ) {
      createBankDataDto.status = 'AWAITING_APPROVAL';
    }

    if (BANK_LIST[createBankDataDto.bankCode]) {
      createBankDataDto.bankName = BANK_LIST[createBankDataDto.bankCode];
    }

    const collaborateRoles = [
      Role.ADMIN,
      Role.FINANCE_ADMIN,
      Role.FINANCE_USER,
      Role.EMPLOYEE,
    ];

    const clientRoles = [Role.CUSTOMER, Role.CUSTOMER_VIEWER];

    const supplierRoles = [Role.SUPPLIER, Role.SUPPLIER_VIEWER];

    const entityType = collaborateRoles.includes(userParam.role)
      ? EntityType.COLLABORATE
      : clientRoles.includes(userParam.role)
        ? EntityType.CUSTOMER
        : supplierRoles.includes(userParam.role)
          ? EntityType.SUPPLIER
          : null;

    if (!entityType) {
      this.logger.error(
        `Role not found for bank data creation: ${userParam.role}, code review`,
      );

      throw new ForbiddenException(
        'User role does not allow bank data creation',
      );
    }

    const bankData = await this.PrismaBankDataRepository.create({
      ...createBankDataDto,
      entityUuid: userParam.id,
      entityType: entityType,
    });

    await this.PrismaBankAccountUpsertDataRepository.create({
      fieldName: 'CREATED',
      entityUuid: requestUser.id,
      entityType: 'COLLABORATE',
      bankData: {
        connect: { id: bankData.id },
      },
    });

    if (userParam.role === Role.EMPLOYEE) {
      const employee = await this.PrismaEmployeeRepository.findByUserId(
        userParam.id,
      );
      userParam['employee'] = employee;
    }

    if (supplierRoles.includes(userParam.role)) {
      const supplier = await this.PrismaSupplierRepository.findByUserId(
        userParam.id,
      );
      userParam['supplier'] = supplier;
    }

    if (clientRoles.includes(userParam.role)) {
      const customer = await this.PrismaCustomerRepository.findByUserId(
        userParam.id,
      );
      userParam['customer'] = customer;
    }

    return this.toResponseDto({
      user: userParam,
      bankData,
    });
  }

  async update({
    requestUserEmail,
    bankDataParamId,
    updateBankDataDto,
  }: {
    requestUserEmail: string;
    bankDataParamId: string;
    updateBankDataDto: UpdateBankDataDto;
  }): Promise<{ bankData: BankDataResponseDto; user: UserDataResponseDto }> {
    const requestUser =
      await this.PrismaUserRepository.findByEmail(requestUserEmail);

    if (!requestUser) {
      throw new NotFoundException('Request user not found');
    }

    const bankData =
      await this.PrismaBankDataRepository.findById(bankDataParamId);

    if (!bankData) {
      throw new NotFoundException('Bank data not found');
    }

    const userParam = await this.PrismaUserRepository.findById(
      bankData.entityUuid,
    );

    if (!userParam) {
      throw new NotFoundException('User not found');
    }

    if (
      requestUser.id !== userParam.id &&
      requestUser.role !== Role.ADMIN &&
      requestUser.role !== Role.FINANCE_ADMIN
    ) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    if (
      requestUser.role !== Role.ADMIN &&
      requestUser.role !== Role.FINANCE_ADMIN &&
      updateBankDataDto.status !== 'AWAITING_APPROVAL'
    ) {
      updateBankDataDto.status = 'AWAITING_APPROVAL';
    }

    for (const [fieldName, newValue] of Object.entries(updateBankDataDto)) {
      if (newValue !== undefined && bankData[fieldName] !== newValue) {
        await this.PrismaBankAccountUpsertDataRepository.create({
          fieldName: fieldName.toUpperCase(),
          oldValue: (bankData[fieldName] as keysTypes)?.toString() || null,
          newValue: (newValue as keysTypes)?.toString() || null,
          entityUuid: requestUser.id,
          entityType: 'COLLABORATE',
          bankData: {
            connect: { id: bankData.id },
          },
        });
      }
    }

    const updatedBankData = await this.PrismaBankDataRepository.update({
      bankDataId: bankData.id,
      data: updateBankDataDto,
    });

    return this.toResponseDto({
      user: userParam,
      bankData: updatedBankData,
    });
  }

  private toResponseDto({
    user,
    bankData,
  }: {
    user: UserResponse;
    bankData: PrismaBankData;
  }): { bankData: BankDataResponseDto; user: UserDataResponseDto } {
    const userResponse = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
    if (user.employee) {
      userResponse['employee'] = {
        id: user.employee.id,
        name: user.employee.name,
        document: user.employee.document,
        status: user.employee.status,
        createdAt: user.employee.createdAt,
        updatedAt: user.employee.updatedAt,
      };
    }

    if (user.supplier) {
      userResponse['supplier'] = {
        id: user.supplier.id,
        name: user.supplier.name,
        document: user.supplier.document,
        status: user.supplier.status,
        createdAt: user.supplier.createdAt,
        updatedAt: user.supplier.updatedAt,
      };
    }

    if (user.customer) {
      userResponse['customer'] = {
        id: user.customer.id,
        name: user.customer.name,
        document: user.customer.document,
        status: user.customer.status,
        createdAt: user.customer.createdAt,
        updatedAt: user.customer.updatedAt,
      };
    }

    return {
      user: userResponse,
      bankData: {
        id: bankData.id,
        bankName: bankData.bankName,
        bankCode: bankData.bankCode,
        accountType: bankData.accountType,
        agencyNumber: bankData.agencyNumber,
        agencyDigit: bankData.agencyDigit,
        accountNumber: bankData.accountNumber,
        accountDigit: bankData.accountDigit,
        accountHolderName: bankData.accountHolderName,
        accountHolderDocument: bankData.accountHolderDocument,
        pixKey: bankData.pixKey,
        pixKeyType: bankData.pixKeyType,
        isDigitalBank: bankData.isDigitalBank,
        status: bankData.status,
        createdAt: bankData.createdAt,
        updatedAt: bankData.updatedAt,
      },
    };
  }

  async findAll({
    limit,
    offset,
    query,
  }: {
    limit: number;
    offset: number;
    query: {
      entityType?: EntityType;
      bankName?: string;
      bankCode?: string;
      accountType?: BankAccountType;
      agencyNumber?: string;
      agencyDigit?: string;
      accountNumber?: string;
      accountDigit?: string;
      accountHolderName?: string;
      accountHolderDocument?: string;
      pixKey?: string;
      pixKeyType?: BankPixKeyType;
      isDigitalBank?: boolean;
      createdAt?: Date;
      updatedAt?: Date;
      status?: Prisma.EnumBankDataStatusNullableFilter | BankDataStatus | null;
    };
  }) {
    const bankDataList = await this.PrismaBankDataRepository.findAll(query);

    const total = bankDataList.length;
    const paginatedData = bankDataList.slice(offset, offset + limit);

    const items = await Promise.all(
      paginatedData.map(async (bankData) => {
        const user = await this.PrismaUserRepository.findById(
          bankData.entityUuid,
        );

        if (!user) return null;

        if (user.role === Role.EMPLOYEE) {
          const employee = await this.PrismaEmployeeRepository.findByUserId(
            user.id,
          );
          user['employee'] = employee;
        }

        const supplierRoles = [Role.SUPPLIER, Role.SUPPLIER_VIEWER];
        if (supplierRoles.includes(user.role)) {
          const supplier = await this.PrismaSupplierRepository.findByUserId(
            user.id,
          );
          user['supplier'] = supplier;
        }

        const clientRoles = [Role.CUSTOMER, Role.CUSTOMER_VIEWER];
        if (clientRoles.includes(user.role)) {
          const customer = await this.PrismaCustomerRepository.findByUserId(
            user.id,
          );
          user['customer'] = customer;
        }

        return this.toResponseDto({ user, bankData });
      }),
    );

    const validItems = items.filter((item) => item !== null);

    return {
      items: validItems,
      total,
      limit,
      offset,
    };
  }

  async findById({
    bankDataId,
    requestUserEmail,
  }: {
    bankDataId: string;
    requestUserEmail: string;
  }): Promise<BankDataResponseDto> {
    const bankData = await this.PrismaBankDataRepository.findById(bankDataId);

    if (!bankData) {
      throw new NotFoundException('Bank data not found');
    }

    const requestUser =
      await this.PrismaUserRepository.findByEmail(requestUserEmail);

    if (!requestUser) {
      throw new NotFoundException('Request user not found');
    }

    if (
      requestUser.id !== bankData.entityUuid &&
      requestUser.role !== Role.ADMIN &&
      requestUser.role !== Role.FINANCE_ADMIN
    ) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    return this.bankDataToResponseDto(bankData);
  }

  private bankDataToResponseDto(bankData: PrismaBankData): BankDataResponseDto {
    return {
      id: bankData.id,
      bankName: bankData.bankName,
      bankCode: bankData.bankCode,
      accountType: bankData.accountType,
      agencyNumber: bankData.agencyNumber,
      agencyDigit: bankData.agencyDigit,
      accountNumber: bankData.accountNumber,
      accountDigit: bankData.accountDigit,
      accountHolderName: bankData.accountHolderName,
      accountHolderDocument: bankData.accountHolderDocument,
      pixKey: bankData.pixKey,
      pixKeyType: bankData.pixKeyType,
      isDigitalBank: bankData.isDigitalBank,
      status: bankData.status,
      createdAt: bankData.createdAt,
      updatedAt: bankData.updatedAt,
    };
  }
}