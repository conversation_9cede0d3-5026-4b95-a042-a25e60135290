import { Test, TestingModule } from '@nestjs/testing';
import { DeleteSupplierUseCase } from './delete-supplier.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { NotFoundException } from '@nestjs/common';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';
import { SupplierClassification } from '@prisma/client';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';
import { User } from '@/core/domain/user.entity';
import { Role } from '@/core/domain/role.enum';

describe('DeleteSupplierUseCase', () => {
  let useCase: DeleteSupplierUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;
  let userRepository: jest.Mocked<UserRepository>;
  let keycloakService: jest.Mocked<KeycloakIdentityProviderService>;

  const mockSupplier = new Supplier(
    'test-id',
    'Test Supplier',
    '12345678901234',
    'Test Trade',
    new Address('Test Street', null, null, null, 'Test City', '12345-678', 'SP'),
    '<EMAIL>',
    SupplierClassification.CORE,
    SupplierType.GAME,
    SupplierStatus.ACTIVE,
    'user-id',
    'created-by',
    new Date('2024-01-01T00:00:00Z'),
    new Date('2024-01-01T00:00:00Z'),
    'created-by',
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findByUserId: jest.fn(),
    };

    const mockUserRepository: jest.Mocked<UserRepository> = {
      findAll: jest.fn(),
      findById: jest.fn(),
      findByIds: jest.fn(),
      findByEmail: jest.fn(),
      findByKeycloakId: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      updateUserKeycloakId: jest.fn(),
    };

    const mockKeycloakService: jest.Mocked<KeycloakIdentityProviderService> = {
      registerUser: jest.fn(),
      deleteUser: jest.fn(),
      assignUserRoles: jest.fn(),
      authenticate: jest.fn(),
      refreshToken: jest.fn(),
      logout: jest.fn(),
      getUserInfo: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteSupplierUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
        {
          provide: 'UserRepository',
          useValue: mockUserRepository,
        },
        {
          provide: KeycloakIdentityProviderService,
          useValue: mockKeycloakService,
        },
      ],
    }).compile();

    useCase = module.get<DeleteSupplierUseCase>(DeleteSupplierUseCase);
    repository =
      module.get<jest.Mocked<SupplierRepositoryPort>>(SUPPLIER_REPOSITORY);
    userRepository = module.get<jest.Mocked<UserRepository>>('UserRepository');
    keycloakService = module.get<jest.Mocked<KeycloakIdentityProviderService>>(KeycloakIdentityProviderService);
  });

  describe('execute', () => {
    it('should delete supplier when found by UUID', async () => {
      const findById = jest.spyOn(repository, 'findById');
      const deleteFn = jest.spyOn(repository, 'delete');

      findById.mockResolvedValue(mockSupplier);

      await useCase.execute({ uuid: 'test-id' });

      expect(findById).toHaveBeenCalledWith('test-id');
      expect(deleteFn).toHaveBeenCalledWith('test-id');
    });

    it('should delete supplier and associated user from Keycloak when user has keycloakId', async () => {
      const mockUser = new User(
        'user-id',
        '<EMAIL>',
        'Test User',
        'password',
        Role.USER,
        new Date(),
        new Date(),
        'keycloak-user-id'
      );

      const findById = jest.spyOn(repository, 'findById');
      const findUserById = jest.spyOn(userRepository, 'findById');
      const deleteUser = jest.spyOn(keycloakService, 'deleteUser');
      const deleteFn = jest.spyOn(repository, 'delete');

      findById.mockResolvedValue(mockSupplier);
      findUserById.mockResolvedValue(mockUser);
      deleteUser.mockResolvedValue();

      await useCase.execute({ uuid: 'test-id' });

      expect(findById).toHaveBeenCalledWith('test-id');
      expect(findUserById).toHaveBeenCalledWith('user-id');
      expect(deleteUser).toHaveBeenCalledWith('keycloak-user-id');
      expect(deleteFn).toHaveBeenCalledWith('test-id');
    });

    it('should delete supplier without calling Keycloak when user has no keycloakId', async () => {
      const mockUser = new User(
        'user-id',
        '<EMAIL>',
        'Test User',
        'password',
        Role.USER,
        new Date(),
        new Date(),
        undefined // no keycloakId
      );

      const findById = jest.spyOn(repository, 'findById');
      const findUserById = jest.spyOn(userRepository, 'findById');
      const deleteUser = jest.spyOn(keycloakService, 'deleteUser');
      const deleteFn = jest.spyOn(repository, 'delete');

      findById.mockResolvedValue(mockSupplier);
      findUserById.mockResolvedValue(mockUser);

      await useCase.execute({ uuid: 'test-id' });

      expect(findById).toHaveBeenCalledWith('test-id');
      expect(findUserById).toHaveBeenCalledWith('user-id');
      expect(deleteUser).not.toHaveBeenCalled();
      expect(deleteFn).toHaveBeenCalledWith('test-id');
    });

    it('should continue deletion even if Keycloak deletion fails', async () => {
      const mockUser = new User(
        'user-id',
        '<EMAIL>',
        'Test User',
        'password',
        Role.USER,
        new Date(),
        new Date(),
        'keycloak-user-id'
      );

      const findById = jest.spyOn(repository, 'findById');
      const findUserById = jest.spyOn(userRepository, 'findById');
      const deleteUser = jest.spyOn(keycloakService, 'deleteUser');
      const deleteFn = jest.spyOn(repository, 'delete');
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      findById.mockResolvedValue(mockSupplier);
      findUserById.mockResolvedValue(mockUser);
      deleteUser.mockRejectedValue(new Error('Keycloak error'));

      await useCase.execute({ uuid: 'test-id' });

      expect(findById).toHaveBeenCalledWith('test-id');
      expect(findUserById).toHaveBeenCalledWith('user-id');
      expect(deleteUser).toHaveBeenCalledWith('keycloak-user-id');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Erro ao deletar usuário do supplier test-id no Keycloak:',
        expect.any(Error)
      );
      expect(deleteFn).toHaveBeenCalledWith('test-id');

      consoleSpy.mockRestore();
    });

    it('should throw SupplierNotFoundError when supplier does not exist', async () => {
      const findById = jest.spyOn(repository, 'findById');

      findById.mockResolvedValue(null);

      await expect(
        useCase.execute({ uuid: 'non-existent-id' }),
      ).rejects.toThrow(NotFoundException);
      expect(findById).toHaveBeenCalledWith('non-existent-id');
    });
  });
});
