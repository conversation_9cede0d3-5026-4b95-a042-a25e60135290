import { Injectable, Inject, NotFoundException, Logger } from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { UserRepository } from '@/core/ports/repositories/user-repository.interface';
import { KeycloakIdentityProviderService } from '@/infrastructure/keycloak/keycloak-identity-provider.service';

export interface DeleteSupplierInput {
  uuid: string;
}

@Injectable()
export class DeleteSupplierUseCase {
  private readonly logger = new Logger(DeleteSupplierUseCase.name);

  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
    @Inject('UserRepository')
    private readonly userRepository: UserRepository,
    private readonly keycloakIdentityProvider: KeycloakIdentityProviderService,
  ) {}

  async execute(input: DeleteSupplierInput): Promise<void> {
    const existingSupplier = await this.supplierRepository.findById(input.uuid);

    if (!existingSupplier) {
      throw new NotFoundException('Fornecedor não encontrado');
    }

    if (existingSupplier.userId) {
      try {
        const user = await this.userRepository.findById(existingSupplier.userId);
        if (user && user.keycloakId) {
          await this.keycloakIdentityProvider.deleteUser(user.keycloakId);
        }
      } catch (error) {
        this.logger.warn(`Erro ao deletar usuário do supplier ${input.uuid} no Keycloak:`, error);
      }
    }

    await this.supplierRepository.delete(input.uuid);
  }
}
